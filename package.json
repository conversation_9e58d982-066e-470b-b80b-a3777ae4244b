{"name": "atelier-ia", "homepage": "https://djaymare.github.io/atelierIA", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "build": "vite build"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.1.8", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^15.15.0", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-svgr": "^4.3.0"}}